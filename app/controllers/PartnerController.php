<?php

require_once 'app/utils/ControllerHelpers.php';

/**
 * Description of PartnerController
 *
 * <AUTHOR>
 */
class PartnerController extends \ControllerBase
{

    /**
     * GetPartners
     * @return type
     */
    public function GetPartners()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Partners";

        $data = $this->request->get();

        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetPartners :" . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'limit', 'skip_cache', 'sort', 'page', 'export', 'status', 'start', 'end'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            $params['limit'] = $extractedData['limit'];
            $params['skipCache'] = $extractedData['skip_cache'];
            $params['sort'] = $extractedData['sort'];
            $page = $extractedData['page'];
            $export = $extractedData['export'];
            $status = $extractedData['status'];
            $start = $extractedData['start'];
            $stop = $extractedData['end'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            if (!$page) {
                $page = 1;
            }

            if (!$params['limit'] || !is_numeric($params['limit'])) {
                $params['limit'] = $this->settings['SelectRecordLimit'];
            }

            if (!in_array($params['skipCache'], [1, 2])) {
                $params['skipCache'] = 1;
            }

            $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
            if (count($order_arr) > 1) {
                $sort = "p." . $this->cleanStrSQL($order_arr[0]) . "";
                $order = isset($order_arr[1]) ? $order_arr[1] : 'DESC';

                if (!in_array(strtoupper($order), ['ASC', 'DESC'])) {
                    $order = 'DESC';
                }
            } else {
                $sort = 'p.id';
                $order = 'DESC';
            }

            // Set default values
            if (!$page) {
                $page = 1;
            }

            if (!$params['limit'] || !is_numeric($params['limit'])) {
                $params['limit'] = $this->settings['SelectRecordLimit'];
            }

            if (!in_array($params['skipCache'], [1, 2])) {
                $params['skipCache'] = 1;
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];


            if (is_numeric($status)) {
                $searchParams[':status'] = $status;
                $searchQuery .= " AND p.status = :status";
            }

            if (($stop != null) && ($start != null)) {
                $searchQuery .= " AND p.created_at BETWEEN :start AND :stop ";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif (($stop != null) && ($start == null)) {
                $searchQuery .= " AND p.created_at <=:stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop == null && $start != null) {
                $searchQuery .= " AND p.created_at>=:start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $orderBy = $sort ? "ORDER BY $sort $order" : "";
                $exportLimit = 50000;
                $sorting = "$orderBy LIMIT $exportLimit";
            }

            // select id,name,status,created_at,address,country,msisdn from partners;
            $sql = "SELECT (SELECT COUNT(p.id) FROM partners p  $searchQuery) as trx_count,"
                . "p.id, p.name, p.status, p.created_at, p.address, p.country, p.msisdn "
                . "FROM partners p $searchQuery $sorting";

            $results = $this->rawSelect('dbUser', $sql, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Partners!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Partners successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * CreatePartner
     * @return type
     */
    public function CreatePartner()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Create Partners";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request CreatePartner :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $name = $data->name ?? false;
        $status = $data->status ?? 'active';
        $address = $data->address ?? null;
        $country = $data->country ?? null;
        $msisdn = $data->msisdn ?? null;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$name) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Mandatory field(s) required!"], true);
        }

        // Validate status
        if (!in_array($status, ['active', 'inactive'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Invalid status. Must be 'active' or 'inactive'"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Authorization Error. Invalid Key!'], true);
            }

            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Check if partner name already exists
            $existingPartner = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partners WHERE name = :name", [':name' => $name]);

            if ($existingPartner) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 409, 'message' => 'Partner name already exists!'], true);
            }

            // Insert new partner
            $partnerId = $this->rawInsertBulk('dbUser', 'partners', [
                'name' => $name,
                'status' => $status,
                'address' => $address,
                'country' => $country,
                'msisdn' => $msisdn,
                'created_at' => $this->now()
            ]);

            if (!$partnerId) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to create partner!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 201, 'message' => 'Partner created successfully!',
                    'data' => ['partner_id' => $partnerId]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdatePartner
     * @return type
     */
    public function UpdatePartner()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Update Partners";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request UpdatePartner :" . json_encode($data));

        // Extract authentication headers using ControllerHelper
        $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());
        $authData['timestamp'] = $data->timestamp ?? false;

        $partnerId = $data->partner_id ?? false;
        $name = $data->name ?? false;
        $status = $data->status ?? false;
        $address = $data->address ?? null;
        $country = $data->country ?? null;
        $msisdn = $data->msisdn ?? null;

        if (!$partnerId) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Partner ID is required!"], true);
        }

        // Validate status if provided
        if ($status && !in_array($status, ['active', 'inactive'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Invalid status. Must be 'active' or 'inactive'"], true);
        }

        try {
            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Get authenticated user data
            $userData = $authResult['user_data'];

            // Check if partner exists
            $existingPartner = $this->rawSelectOneRecord('dbUser',
                "SELECT id, name FROM partners WHERE id = :id", [':id' => $partnerId]);

            if (!$existingPartner) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner not found!'], true);
            }

            // Check if new name conflicts with existing partner (if name is being updated)
            if ($name && $name !== $existingPartner['name']) {
                $nameConflict = $this->rawSelectOneRecord('dbUser',
                    "SELECT id FROM partners WHERE name = :name AND id != :id",
                    [':name' => $name, ':id' => $partnerId]);

                if ($nameConflict) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200, 'Request is not successful',
                        ['code' => 409, 'message' => 'Partner name already exists!'], true);
                }
            }

            // Build update query dynamically
            $updateFields = [];
            $updateParams = [':id' => $partnerId];

            if ($name) {
                $updateFields[] = "name = :name";
                $updateParams[':name'] = $name;
            }
            if ($status) {
                $updateFields[] = "status = :status";
                $updateParams[':status'] = $status;
            }
            if ($address !== null) {
                $updateFields[] = "address = :address";
                $updateParams[':address'] = $address;
            }
            if ($country !== null) {
                $updateFields[] = "country = :country";
                $updateParams[':country'] = $country;
            }
            if ($msisdn !== null) {
                $updateFields[] = "msisdn = :msisdn";
                $updateParams[':msisdn'] = $msisdn;
            }

            if (empty($updateFields)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 422, 'message' => 'No fields to update!'], true);
            }

            $updateSql = "UPDATE partners SET " . implode(', ', $updateFields) . " WHERE id = :id";
            $result = $this->rawUpdateWithParams('dbUser', $updateSql, $updateParams);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to update partner!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Partner updated successfully!'], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * DeletePartner
     * @return type
     */
    public function DeletePartner()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Delete Partners";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request DeletePartner :" . json_encode($data));

        // Extract authentication headers using ControllerHelper
        $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());
        $authData['timestamp'] = $data->timestamp ?? false;

        $partnerId = $data->partner_id ?? false;

        if (!$partnerId) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Partner ID is required!"], true);
        }

        try {
            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Get authenticated user data
            $userData = $authResult['user_data'];

            // Check if partner exists
            $existingPartner = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partners WHERE id = :id", [':id' => $partnerId]);

            if (!$existingPartner) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner not found!'], true);
            }

            // Check for dependencies (partner services, settings, etc.)
            $dependencies = $this->rawSelectOneRecord('dbUser',
                "SELECT COUNT(*) as count FROM partner_services WHERE partner_id = :id",
                [':id' => $partnerId]);

            if ($dependencies && $dependencies['count'] > 0) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 409, 'message' => 'Cannot delete partner with existing services!'], true);
            }

            // Soft delete by setting status to inactive
            $result = $this->rawUpdateWithParams('dbUser',
                "UPDATE partners SET status = 'inactive' WHERE id = :id",
                [':id' => $partnerId]);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to delete partner!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Partner deleted successfully!'], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }



/**
GetPartnerSettings
* @return type
*/
    public function GetPartnerSettings()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Settings";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetPartnerSettings :" . json_encode($data));

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'partner_id', 'bet_id', 'sport_id', 'status', 'live_bet'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Build pagination and sorting
            $pagination = ControllerHelpers::buildPaginationParams($data);
            $sorting = ControllerHelpers::buildSortingQuery($pagination, 'pbs.slip_id', 'DESC');

            // Build search query
            $searchQuery = "WHERE 1=1";
            $searchParams = [];

            if ($params['partner_id']) {
                $searchParams[':partner_id'] = $params['partner_id'];
                $searchQuery .= " AND pbs.partner_id = :partner_id";
            }

            if ($params['bet_id']) {
                $searchParams[':bet_id'] = $params['bet_id'];
                $searchQuery .= " AND pbs.bet_id = :bet_id";
            }

            if ($params['sport_id']) {
                $searchParams[':sport_id'] = $params['sport_id'];
                $searchQuery .= " AND pbs.sport_id = :sport_id";
            }

            if ($params['status']) {
                $searchParams[':status'] = $params['status'];
                $searchQuery .= " AND pbs.status = :status";
            }

            if ($params['live_bet']) {
                $searchParams[':live_bet'] = $params['live_bet'];
                $searchQuery .= " AND pbs.live_bet = :live_bet";
            }

            if ($pagination['export'] == 1) {
                $sorting = "";
            }

            // Execute query
            $query = "SELECT (SELECT COUNT(pbs.slip_id) FROM partners_bet_slips pbs $searchQuery) as trx_count,
                      pbs.slip_id, pbs.partner_id, p.name as partner_name, pbs.bet_id, pbs.sport_id,
                      pbs.parent_match_id, pbs.parent_market_id, pbs.market_id, pbs.selection_id,
                      pbs.outcome_name, pbs.odd_value, pbs.pick, pbs.pick_name, pbs.winning_outcome,
                      pbs.ht_scores, pbs.ft_scores, pbs.et_scores, pbs.extra_data, pbs.live_bet,
                      pbs.status, pbs.resulting_type, pbs.start_time, pbs.created_at, pbs.updated_at
                      FROM partners_bet_slips pbs
                      LEFT JOIN partners p ON pbs.partner_id = p.id
                      $searchQuery $sorting";

            $results = $this->rawSelect('dbUser', $query, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful', ['code' => 404, 'message' => 'No partner bet slips found.'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Queried ' . $results[0]['trx_count'] . ' partner bet slips successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'], 'result' => $results]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * CreatePartnerSettings
     * @return type
     */
    public function CreatePartnerSettings()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Create Partner Settings";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request CreatePartnerSettings :" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;
        $accessToken = $headers['x-access'] ?? false;

        $partnerId = $data->partner_id ?? false;
        $apiKey = $data->api_key ?? false;
        $ipAddress = $data->ip_address ?? null;
        $callbackUrl = $data->callback_url ?? null;
        $currency = $data->currency ?? 'USD';
        $denomination = $data->denomination ?? 'cents';
        $timezone = $data->timezone ?? 'UTC';
        $billingMode = $data->billing_mode ?? 'prepay';
        $rateLimit = $data->rate_limit ?? 60;
        $websites = $data->websites ?? null;
        $version = $data->version ?? 1;

        if (!$Authorization || !$appKey || !$hashKey || !$accessToken || !$partnerId || !$apiKey) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Mandatory field(s) required!"], true);
        }

        // Validate billing mode
        if (!in_array($billingMode, ['prepay', 'postpay'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Invalid billing mode. Must be 'prepay' or 'postpay'"], true);
        }

        // Validate rate limit
        if (!is_numeric($rateLimit) || $rateLimit < 1 || $rateLimit > 10000) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Rate limit must be between 1 and 10000"], true);
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 401, 'message' => 'Authorization Error. Invalid Key!'], true);
            }

            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Validate partner exists
            $partner = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partners WHERE id = :id AND status = 'active'", [':id' => $partnerId]);

            if (!$partner) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner not found or inactive!'], true);
            }

            // Check if partner settings already exist
            $existingSettings = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partner_settings WHERE partner_id = :partner_id",
                [':partner_id' => $partnerId]);

            if ($existingSettings) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 409, 'message' => 'Partner settings already exist!'], true);
            }

            // Check if API key is unique
            $existingApiKey = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partner_settings WHERE api_key = :api_key",
                [':api_key' => $apiKey]);

            if ($existingApiKey) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 409, 'message' => 'API key already exists!'], true);
            }

            // Insert new partner settings
            $partnerSettingsId = $this->rawInsertBulk('dbUser', 'partner_settings', [
                'partner_id' => $partnerId,
                'api_key' => $apiKey,
                'ip_address' => $ipAddress,
                'callback_url' => $callbackUrl,
                'currency' => $currency,
                'denomination' => $denomination,
                'timezone' => $timezone,
                'billing_mode' => $billingMode,
                'rate_limit' => $rateLimit,
                'websites' => $websites ? json_encode($websites) : null,
                'version' => $version,
                'created_at' => $this->now()
            ]);

            if (!$partnerSettingsId) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to create partner settings!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 201, 'message' => 'Partner settings created successfully!',
                    'data' => ['partner_settings_id' => $partnerSettingsId]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdatePartnerSettings
     * @return type
     */
    public function UpdatePartnerSettings($partnerSettingsId)
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Update Partner Settings";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request UpdatePartnerSettings :" . json_encode($data));

        // Extract authentication headers using ControllerHelper
        $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());
        $authData['timestamp'] = $data->timestamp ?? false;

        $ipAddress = $data->ip_address ?? null;
        $callbackUrl = $data->callback_url ?? null;
        $currency = $data->currency ?? null;
        $denomination = $data->denomination ?? null;
        $timezone = $data->timezone ?? null;
        $billingMode = $data->billing_mode ?? null;
        $rateLimit = $data->rate_limit ?? null;
        $websites = $data->websites ?? null;
        $version = $data->version ?? null;

        // Validate billing mode if provided
        if ($billingMode !== null && !in_array($billingMode, ['prepay', 'postpay'])) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Invalid billing mode. Must be 'prepay' or 'postpay'"], true);
        }

        // Validate rate limit if provided
        if ($rateLimit !== null && (!is_numeric($rateLimit) || $rateLimit < 1 || $rateLimit > 10000)) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200, 'Request is not successful',
                ['code' => 422, 'message' => "Rate limit must be between 1 and 10000"], true);
        }

        try {
            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Get authenticated user data
            $userData = $authResult['user_data'];

            // Validate partner settings exist
            $partnerSettings = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partner_settings WHERE id = :id", [':id' => $partnerSettingsId]);

            if (!$partnerSettings) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner settings not found!'], true);
            }

            // Build update query dynamically
            $updateFields = [];
            $updateParams = [':id' => $partnerSettingsId];

            if ($ipAddress !== null) {
                $updateFields[] = "ip_address = :ip_address";
                $updateParams[':ip_address'] = $ipAddress;
            }
            if ($callbackUrl !== null) {
                $updateFields[] = "callback_url = :callback_url";
                $updateParams[':callback_url'] = $callbackUrl;
            }
            if ($currency !== null) {
                $updateFields[] = "currency = :currency";
                $updateParams[':currency'] = $currency;
            }
            if ($denomination !== null) {
                $updateFields[] = "denomination = :denomination";
                $updateParams[':denomination'] = $denomination;
            }
            if ($timezone !== null) {
                $updateFields[] = "timezone = :timezone";
                $updateParams[':timezone'] = $timezone;
            }
            if ($billingMode !== null) {
                $updateFields[] = "billing_mode = :billing_mode";
                $updateParams[':billing_mode'] = $billingMode;
            }
            if ($rateLimit !== null) {
                $updateFields[] = "rate_limit = :rate_limit";
                $updateParams[':rate_limit'] = $rateLimit;
            }
            if ($websites !== null) {
                $updateFields[] = "websites = :websites";
                $updateParams[':websites'] = json_encode($websites);
            }
            if ($version !== null) {
                $updateFields[] = "version = :version";
                $updateParams[':version'] = $version;
            }

            if (empty($updateFields)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 422, 'message' => 'No fields to update!'], true);
            }

            $updateSql = "UPDATE partner_settings SET " . implode(', ', $updateFields) . " WHERE id = :id";
            $result = $this->rawUpdateWithParams('dbUser', $updateSql, $updateParams);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to update partner settings!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Partner settings updated successfully!'], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

}