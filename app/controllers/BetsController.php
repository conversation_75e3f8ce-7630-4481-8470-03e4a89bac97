<?php

require_once 'app/utils/ControllerHelpers.php';

class BetsController extends \ControllerBase
{




    /**
     * GetPartnerBets
     * @return type
     */
    function GetPartnerBets()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Bets";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetPartnerBets :" . json_encode($data));

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'partner_id', 'bet_currency', 'bet_type', 'status', 'start_date', 'end_date', 'profile_id'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                $errorData = $errorResponse['error'];
                if ($errorResponse['technical_details']) {
                    $errorData['technical_message'] = $errorResponse['technical_details'];
                }
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorData, true);
            }

            // Build pagination and sorting
            $pagination = ControllerHelpers::buildPaginationParams($data);
            $sorting = ControllerHelpers::buildSortingQuery($pagination, 'pb.bet_id', 'DESC');

            // Build search query
            $searchQuery = "WHERE 1=1";
            $searchParams = [];

            if ($params['partner_id']) {
                $searchParams[':partner_id'] = $params['partner_id'];
                $searchQuery .= " AND pb.partner_id = :partner_id";
            }

            if ($params['profile_id']) {
                $searchParams[':profile_id'] = $params['profile_id'];
                $searchQuery .= " AND pb.profile_id = :profile_id";
            }

            if ($params['bet_currency']) {
                $searchParams[':bet_currency'] = $params['bet_currency'];
                $searchQuery .= " AND pb.bet_currency = :bet_currency";
            }

            if ($params['bet_type']) {
                $searchParams[':bet_type'] = $params['bet_type'];
                $searchQuery .= " AND pb.bet_type = :bet_type";
            }

            if ($params['status']) {
                $searchParams[':status'] = $params['status'];
                $searchQuery .= " AND pb.status = :status";
            }

            if ($params['start_date']) {
                $searchParams[':start_date'] = $params['start_date'];
                $searchQuery .= " AND DATE(pb.created_at) >= :start_date";
            }

            if ($params['end_date']) {
                $searchParams[':end_date'] = $params['end_date'];
                $searchQuery .= " AND DATE(pb.created_at) <= :end_date";
            }

            if ($pagination['export'] == 1) {
                $sorting = "";
            }

            // Execute query with betting-specific context
            $query = "SELECT (SELECT COUNT(pb.bet_id) FROM partners_bets pb $searchQuery) as trx_count,
                      pb.bet_id, pb.partner_id, p.name as partner_name, pb.profile_id, pb.bet_currency,
                      pb.bet_amount, pb.bet_reference, pb.bet_transaction_id, pb.bet_credit_transaction_id,
                      pb.bet_type, pb.total_games, pb.live_events, pb.total_odd, pb.possible_win,
                      pb.witholding_tax, pb.excise_tax, pb.bet_attribution, pb.browser_details,
                      pb.extra_data, pb.created_by, pb.kra_report, pb.risk_state, pb.processed,
                      pb.status, pb.created_at, pb.updated_at,
                      CASE
                        WHEN pb.status = 0 THEN 'Pending'
                        WHEN pb.status = 1 THEN 'Won'
                        WHEN pb.status = 2 THEN 'Lost'
                        WHEN pb.status = 3 THEN 'Cancelled'
                        ELSE 'Unknown'
                      END as status_description
                      FROM partners_bets pb
                      LEFT JOIN partners p ON pb.partner_id = p.id
                      $searchQuery $sorting";

            $results = $this->rawSelect('dbBetsRead', $query, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful', ['code' => 404, 'message' => 'No partner bets found.'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Queried ' . $results[0]['trx_count'] . ' partner bets successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'], 'result' => $results]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }
    /**
     * GetPartnersBetSlips
     * @return type
     */
    function GetPartnersBetSlips()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Bet Slips";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'partner_id', 'bet_id', 'sport_id', 'status', 'live_bet'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                $errorData = $errorResponse['error'];
                if ($errorResponse['technical_details']) {
                    $errorData['technical_message'] = $errorResponse['technical_details'];
                }
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorData, true);
            }

            // Extract pagination and search parameters
            $pagination = ControllerHelpers::extractPaginationParams($data, $this->settings['SelectRecordLimit']);
            $searchParams = ControllerHelpers::extractSearchParams($data);
            $dateRange = ControllerHelpers::extractDateRange($data);

            // Build search query
            $searchQuery = "WHERE 1=1";
            $queryParams = [];

            if ($params['partner_id']) {
                $searchQuery .= " AND pbs.partner_id = :partner_id";
                $queryParams[':partner_id'] = $params['partner_id'];
            }

            if ($params['bet_id']) {
                $searchQuery .= " AND pbs.bet_id = :bet_id";
                $queryParams[':bet_id'] = $params['bet_id'];
            }

            if ($params['sport_id']) {
                $searchQuery .= " AND pbs.sport_id = :sport_id";
                $queryParams[':sport_id'] = $params['sport_id'];
            }

            if ($params['status'] !== false) {
                $searchQuery .= " AND pbs.status = :status";
                $queryParams[':status'] = $params['status'];
            }

            if ($params['live_bet'] !== false) {
                $searchQuery .= " AND pbs.live_bet = :live_bet";
                $queryParams[':live_bet'] = $params['live_bet'];
            }

            if ($dateRange['start_date']) {
                $searchQuery .= " AND DATE(pbs.created_at) >= :start_date";
                $queryParams[':start_date'] = $dateRange['start_date'];
            }

            if ($dateRange['end_date']) {
                $searchQuery .= " AND DATE(pbs.created_at) <= :end_date";
                $queryParams[':end_date'] = $dateRange['end_date'];
            }

            // Build sorting
            $sorting = $this->tableQueryBuilder($searchParams['sort'], $searchParams['order'],
                $pagination['page'], $pagination['limit']);

            if ($pagination['export'] == 1) {
                $sorting = "";
            }

            // Execute query
            $query = "SELECT (SELECT COUNT(pbs.slip_id) FROM partners_bet_slips pbs $searchQuery) as trx_count,
                      pbs.slip_id, pbs.partner_id, p.name as partner_name, pbs.bet_id, pbs.sport_id,
                      pbs.parent_match_id, pbs.parent_market_id, pbs.market_id, pbs.selection_id,
                      pbs.outcome_name, pbs.odd_value, pbs.pick, pbs.pick_name, pbs.winning_outcome,
                      pbs.ht_scores, pbs.ft_scores, pbs.et_scores, pbs.extra_data, pbs.live_bet,
                      pbs.status, pbs.resulting_type, pbs.start_time, pbs.created_at, pbs.updated_at
                      FROM partners_bet_slips pbs
                      LEFT JOIN partners p ON pbs.partner_id = p.id
                      $searchQuery $sorting";

            $results = $this->rawSelect('dbUser', $query, $queryParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful', ['code' => 404, 'message' => 'No partner bet slips found!'], true);
            }

            // Format numeric values
            foreach ($results as &$result) {
                $result['odd_value'] = number_format((float)$result['odd_value'], 2, '.', '');
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count'] . ' Partner Bet Slips successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'], 'result' => $results]
                ], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }


}
