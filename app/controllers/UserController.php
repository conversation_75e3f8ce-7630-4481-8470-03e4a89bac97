<?php

require_once 'app/utils/ControllerHelpers.php';

/**
 * UserController
 */
class UserController extends \ControllerBase
{

    /**
     * UserAccountLogin
     * @return type
     */
    function UserAccountLogin()
    {
        $start = $this->getMicrotime();
        $data = $this->request->getJsonRawBody();
        $string = (preg_replace('/"password":"[^"]*?"/', '"password":***password***'
            , json_encode($this->request->getJsonRawBody()) . PHP_EOL));

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . " | Request:$string");

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;

        $params['username'] = $data->username ?? false;
        $password = $data->password ?? false;
        $params['dial_code'] = $data->dial_code ?? false;


        if (!$Authorization || !$hashKey || !$appKey || !$password || !$params['username']) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) are required!!"], true);
        }

        $username = trim($params['username']);
        $password = base64_decode($password);

        if (!$this->validateEmail($params['username'])) {
            if ($params['dial_code']) {
                $params['dial_code'] = $this->settings['mnoApps']['DefaultDialCode'];
            }
            $params['username'] = $this->formatMobileNumber($params['username'], $params['dial_code']);
            $params['network'] = $this->getMobileNetwork($params['username'], $params['dial_code']);
            if ($params['network'] == 'UNKNOWN') {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 422,
                        'message' => "Validation Error. Invalid Phone Number!"], true);
            }
        }

        $username = $params['username'];

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);

            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Service Keys>..!'], true);
            }

            if (!in_array($channel['channelName'], ['ADMIN_WEB'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid App!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $user = $this->rawSelectOneRecord('dbUser',
                "SELECT user.id user_id,user.user_name,user.display_name,user.type,user.password,user.msisdn,user_login.role_id"
                . ",user_login.failed_attempts,user_login.blocked_timeline,user_login.status"
                . ",user_login.activation_date,user_login.status,DATEDIFF(NOW()"
                . ",user_login.next_passwd_change_date) as change_password_in_days"
                . ",user_roles.name as role_name"
                . ",IFNULL(user_login.permission_acl,user_roles.permissions_acl) as permission_acl "
                . "FROM user JOIN user_login ON user.id=user_login.user_id "
                . "JOIN user_roles ON user_login.role_id=user_roles.id "
                . "WHERE user.status=1 AND (user.user_name=:username OR user.msisdn=:username)",
                [':username' => $username]);

            if (!$user) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid user or password!'], true);
            }

            if (UserUtils::CheckBlackListAccount($user['user_id'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => 'Authorization Denied. Account is blacklisted!'], true);
            }

            if (!$user['activation_date']) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 410,
                        'message' => 'Authorization Error. '
                            . 'User account is not activated!'], true);
            }

            $blocked_timeline = $user['blocked_timeline'] ?? $this->now();
            $updateParams = [':user_id' => $user['user_id']];

            if ($this->Decrypt($user['password']) != (md5("$password$" . $user['user_id']))) {
                $updatSql = "UPDATE user_login SET failed_attempts = failed_attempts+1"
                    . ", cumlative_failed_attempts = cumlative_failed_attempts+1"
                    . ", last_failed_attempt = NOW()";

                if ($user ['failed_attempts'] >= $this->settings['Authentication']['MaxAttempts']) {
                    $updatSql .= ", blocked_timeline = :blocked_timeline";
                    $updateParams[':blocked_timeline'] = date("Y-m-d H:i:s", strtotime($blocked_timeline
                        . " +" . $this->settings['Authentication'] ['FailedDelay'] . " minute"));
                }

                $updatSql .= " WHERE user_id = :user_id LIMIT 1";
                if (!$this->rawUpdateWithParams('dbUser', $updatSql, $updateParams)) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 400,
                            'message' => 'Authorization Error. Auth Error!'], true);
                }

                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401, 'message' => "Invalid Password or Username!"], true);
            }

            if ($user['status'] != $this->settings['UserStatus']['Active']) {
                if ($user['status'] == $this->settings['UserStatus']['Suspended']) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 503,
                            'message' => 'Account access denied due '
                                . 'to violation(s). Contact customer support for assistance!'], true);
                }

                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 205,
                        'message' => 'Verification Error. Account is not verified yet. '
                            . 'Use the code that was sent via sms.!'], true);
            }

            $key = $this->settings['ServerName'] . "#AuthKey#$" . $user['user_id'];
//            RedisUtils::redisRawDelete($key);

            if ($user['blocked_timeline']) {
                $checkTimeline = $this->rawSelectOneRecord('dbUser',
                    "SELECT blocked_timeline FROM user_login WHERE user_id=:user_id "
                    . "AND blocked_timeline>=NOW()", [':user_id' => $user['user_id']]);

                if ($checkTimeline) {
                    $blocked = date("Y-m-d H:i:s", strtotime($this->now("Y-m-d H:i:s")
                        . " +" . $this->settings['Authentication'] ['FailedDelay'] . " minute"));

                    $this->rawUpdateWithParams('dbUser',
                        "UPDATE `user_login` SET `failed_attempts`=failed_attempts+1"
                        . ",cumlative_failed_attempts=cumlative_failed_attempts+1"
                        . ",blocked_timeline=:blocked WHERE user_id=:user_id LIMIT 1",
                        [':user_id' => $user['user_id'], ':blocked' => $blocked]);

                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 401,
                            'message' => "Authorization Error. Account "
                                . "is on a Lock Timeout until $blocked!"], true);
                }

                $user['failed_attempts'] = 0;
            }

            if ($user ['failed_attempts'] >= $this->settings['Authentication']['MaxAttempts']) {

                $blocked = date("Y-m-d H:i:s", strtotime($this->now("Y-m-d H:i:s")
                    . " +" . $this->settings['Authentication'] ['FailedDelay'] . " minute"));

                $this->rawUpdateWithParams('dbUser',
                    "UPDATE `user_login` SET `failed_attempts`=failed_attempts+1"
                    . ",cumlative_failed_attempts=cumlative_failed_attempts+1"
                    . ",blocked_timeline=:blocked WHERE user_id=:user_id LIMIT 1",
                    [':user_id' => $user['user_id'], ':blocked' => $blocked]);

                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => "Authorization Error. User is "
                            . "BLOCKED from the system till  $blocked!"], true);
            }

            $issuedAt = new \DateTimeImmutable();
            $exp = $issuedAt->modify('+' . $this->settings['Authentication'] ['TokenExpiryTime'] . ' minutes');

            $uData = [
                'uid' => $user['user_id'],
                'username' => $user['user_name'],
                'st' => 1,
                'rid' => $user['role_id'],
                'exp' => $exp->getTimestamp(),
                'iat' => $issuedAt->getTimestamp(),
                'nbf' => $issuedAt->getTimestamp(),
                'iss' => md5($this->settings['ServerName']),
                'ip' => $this->getClientIPServer()];

            $access_token = $this->Encrypt(json_encode($uData));
            $token = base64_encode(md5($access_token));

            if (!$this->rawUpdateWithParams('dbUser',
                "UPDATE user_login SET failed_attempts = 0"
                . ",reset_attempts = 0"
                . ",access_token=:access_token"
                . ",success_attempts = success_attempts+1"
                . ",cumulative_success_login=cumulative_success_login+1"
                . ",last_logged_on= NOW()"
                . ",blocked_timeline = NULL"
                . ",access_token_expiry_date=:access_token_expiry_date "
                . "WHERE user_id = :user_id LIMIT 1",
                [':user_id' => $user['user_id'],
                    ':access_token_expiry_date' => $exp->format('Y-m-d H:i:s'),
                    ':access_token' => $token])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => "Authorization Error. Grant Access failed"], true);
            }

            $key = $this->settings['ServerName'] . "#AuthKey#$" . $user['user_id'];
            RedisUtils::redisRawInsertData($key,
                ['api_key' => $access_token],
                60 * $this->settings['Authentication'] ['TokenExpiryTime'] * 1);

            if ($user['change_password_in_days'] > 0) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 201,
                        'message' => "Password requires resets every "
                            . $this->settings['Authentication'] ['NextResetDays'] . " days"],
                    true, $token);
            }

            $permission_acl = [];
            if ($user['permission_acl']) {
                $permission_acl = UserUtils::GetUserPermissions(str_replace(':', ',', $user['permission_acl']));
            }

            // 2FA Implementation - Phase 1: Generate and send OTP
            $otp_code = $this->randStrGen(5);

            // Set OTP expiry time
            $issuedAt = new \DateTimeImmutable();
            $otp_expiry_date = $issuedAt
                ->modify('+' . $this->settings['Authentication']['ResetDelay'] . ' minutes')
                ->format('Y-m-d H:i:s');


            // Store OTP in database for verification - set status to 9 (pending OTP verification)
            $updateParams = [
                ':verification_code' => $this->Encrypt($otp_code . "$" . $user['user_id']),
                ':user_id' => $user['user_id'],
                ':reset_expiry_date' => $otp_expiry_date,
                ':access_token' => $token
            ];

            $updateSql = "UPDATE user_login SET verification_code=:verification_code"
                . ",reset_expiry_date=:reset_expiry_date"
                . ",access_token=:access_token"
                . ",reset_attempts=0"
                . " WHERE user_id=:user_id LIMIT 1";

            if (!$this->rawUpdateWithParams('dbUser', $updateSql, $updateParams)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 500,
                        'message' => 'Login Error. System is busy!'], true);
            }

            // Send SMS with OTP
            $msgUtils = new Messaging();
            $unique_id = $this->ReferenceNumber();
            $smsResult = null;

            // Check if user has a valid mobile number (msisdn)
            if ($this->validateMobile($params['username'])) {
                $smsResult = $msgUtils->SendSMs(
                    ['msisdn' => $user['msisdn'],
                        'message' => "Your login verification code is: $otp_code. Valid for "
                            . $this->settings['Authentication']['ResetDelay'] . " minutes.",
                        'callback_url' => '',
                        'user_id' => $user['user_id'],
                        'message_type' => 'NOTIFICATIONS',
                        'unique_id' => $unique_id]);

                $this->infologger->info(__LINE__ . ":" . __CLASS__
                    . "|" . $this->CalculateTAT($start) . " Sec(s)"
                    . "| [" . $user['msisdn'] . "]"
                    . "| UniqueId: $unique_id"
                    . "| SendSMs(). 2FA Code: $otp_code"
                    . "| Result: " . json_encode($smsResult));
            } elseif ($this->validateEmail($user['user_name'])) {
                $email_body = '<p>'
                    . '<h2>Hello, your login verification code is ' . '</br>' . $otp_code . '</h2>'
                    . '</br>Helpline: ' . $this->settings ['Helpline']
                    . '</p>';
                $result = $msgUtils->SendEmail(
                    ['from' => '<EMAIL>',
                        'decription' => 'Login Verification Service'],
                    [$user['user_name']], 'MB Login Verification',
                    $email_body);
                $this->infologger->info(__LINE__ . ":" . __CLASS__
                    . "|" . $this->CalculateTAT($start) . " Sec(s)"
                    . "| [" . $user['user_name'] . "]"
                    . "| UniqueId: $unique_id"
                    . "| SendEmail(). 2FA Code: $otp_code");
            } else {
                $this->infologger->info(__LINE__ . ":" . __CLASS__
                    . "| No valid MSISDN found for user: " . $user['user_id']
                    . "| 2FA Code: $otp_code (not sent via SMS)");
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,  // 202 = Accepted, pending verification
                    'message' => "Login successful. Please verify with the OTP sent to your mobile number.",
                    'data' => [
                        'uid' => $user['user_id'],
                        'username' => $user['user_name'],
                        'display_name' => $user['display_name'],
                        'user_type' => $user['type'],
                        'role_name' => $user['role_name'],
//                        'permissions' => $permission_acl,
                        'requires_otp' => true,
                        'otp_expires_in' => $this->settings['Authentication']['ResetDelay'],
                        'otp_sent' => $smsResult ? $smsResult['success'] : false
                    ]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * VerifyLoginOTP
     * @return type
     */
    function VerifyLoginOTP()
    {
        $start = $this->getMicrotime();

        $data = (array)$this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . " | Request VerifyLoginOTP:" . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['username', 'verification_code', 'dial_code'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);

            $params['username'] = $extractedData['username'] ?: false;
            $params['otp_code'] = $extractedData['verification_code'] ?: false;
            $params['dial_code'] = $extractedData['dial_code'] ?: false;

            // Validate required fields
            if (!$params['username'] || !$params['otp_code']) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 422,
                        'message' => "Username and OTP code are required!"], true);
            }

        if (!$params['dial_code']) {
            $params['dial_code'] = $this->settings['mnoApps']['DefaultDialCode'];
        }

        if (!$this->validateEmail($params['username'])) {
            $params['username'] = $this->formatMobileNumber($params['username'], $params['dial_code']);
            $params['network'] = $this->getMobileNetwork($params['username'], $params['dial_code']);
            if ($params['network'] == 'UNKNOWN') {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 422,
                        'message' => 'Validation Error. Invalid Phone Number!'], true);
            }
        }


            // Get user data including verification code and expiry - only Partner type users
            $userData = $this->rawSelectOneRecord('dbUser',
                "SELECT user.id user_id,user.user_name,user.display_name,user.msisdn,user.type,user_login.role_id,user_roles.name as role_name,user_login.status"
                . ",user_login.verification_code,user_login.blocked_timeline,user_login.access_token"
                . ",user_login.reset_attempts,user_login.reset_expiry_date"
                . ",IFNULL(user_login.permission_acl,user_roles.permissions_acl) as permission_acl "
                ." FROM user "
                . "JOIN user_login ON user.id=user_login.user_id "
                . "JOIN user_roles ON user_login.role_id=user_roles.id "
                . "WHERE user.status=1 AND user_roles.status=1 AND (user.user_name=:username OR user.msisdn=:username)",
                [':username' => $params['username']]);

//            $this->infologger->info(__LINE__ . ":" . __CLASS__
//                . " | VerifyLoginOTP userData:" . json_encode($userData)
//                . " | VerifyLoginOTP params:" . json_encode($params)
//                . " | VerifyLoginOTP params:" . json_encode($params['username'])
//            );

            if (!$userData) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Account Error. User account not found or invalid!'], true);
            }

            // Validate that user has partner associations
            if ($userData['type'] == 'Partner') {
                $partnerAssociation = $this->rawSelectOneRecord('dbUser',
                    "SELECT id FROM partners WHERE user_id = :user_id AND status = 1",
                    [':user_id' => $userData['user_id']]);

                if (!$partnerAssociation) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 403,
                            'message' => 'Access Denied. User has no valid partner associations!'], true);
                }
            }

            if (UserUtils::CheckBlackListAccount($userData['user_id'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => 'Authorization Denied. Account is blacklisted!'], true);
            }

            // Check OTP expiry
            if ($userData['reset_expiry_date']) {
                $resetHoldTime = $this->rawSelectOneRecord('dbUser',
                    "SELECT IFNULL(TIMEDIFF(CURRENT_TIMESTAMP(),reset_expiry_date),0) reset_minutes "
                    . "FROM user_login WHERE user_id=:user_id",
                    [':user_id' => $userData['user_id']]);
                if ($resetHoldTime['reset_minutes'] > 0) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 423,
                            'message' => 'OTP Verification Error. OTP has expired. Please login again.'], true);
                }
            }

            // Verify OTP
            if ($this->Decrypt($userData['verification_code']) != ($params['otp_code'] . "$" . $userData['user_id'])) {
//            if ($userData['verification_code'] != ($params['otp_code'] . "$" . $userData['user_id'])) {
                // Increment reset attempts
                if ($userData['reset_attempts'] >= $this->settings['Authentication']['MaxResetAttempts']) {
                    $blocked_time = date("Y-m-d H:i:s", strtotime($this->now() . " +15 minute"));
                    $this->rawUpdateWithParams('dbUser',
                        "UPDATE user_login SET reset_attempts=reset_attempts+1"
                        . ",failed_reset_attempts=failed_reset_attempts+1"
                        . ",blocked_timeline=:blocked_time WHERE user_id=:user_id LIMIT 1",
                        [':user_id' => $userData['user_id'], ':blocked_time' => $blocked_time]);

                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 401,
                            'message' => 'OTP Verification Error. Maximum attempts reached. Account temporarily blocked.'], true);
                }

                $this->rawUpdateWithParams('dbUser',
                    "UPDATE user_login SET reset_attempts=reset_attempts+1"
                    . ",failed_reset_attempts=failed_reset_attempts+1"
                    . " WHERE user_id=:user_id LIMIT 1", [':user_id' => $userData['user_id']]);

                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'OTP Verification Error. Invalid OTP provided!'], true);
            }

            // OTP verified successfully - generate token and complete login
            $issuedAt = new \DateTimeImmutable();
            $exp = $issuedAt->modify('+' . $this->settings['Authentication']['TokenExpiryTime'] . ' minutes');

            // Generate encrypted token (for Redis storage)
            $access_token = $this->Encrypt(json_encode([
                'uid' => $userData['user_id'],
                'username' => $userData['user_name'],
                'st' => 1,
                'rid' => $userData['role_id'],
                'exp' => $exp->getTimestamp(), // Use timestamp for expiry
                'iat' => $issuedAt->getTimestamp(),
                'nbf' => $issuedAt->getTimestamp(),
                'iss' => md5($this->settings['ServerName']),
                'ip' => $this->getClientIPServer()
            ]));

            // Generate API token (for database storage)
            $api_token = base64_encode(md5($access_token));

            // Update user_login and store token
            $this->rawUpdateWithParams('dbUser',
                "UPDATE user_login SET reset_attempts=0,failed_attempts=0,reset_expiry_date=null"
                . ",blocked_timeline=null,verification_code='',last_logged_on=NOW(),status=1"
                . ",success_attempts=success_attempts+1,cumulative_success_login=cumulative_success_login+1"
                . ",access_token=:access_token,access_token_expiry_date=:access_token_expiry_date"
                . " WHERE user_id=:user_id LIMIT 1",
                [':user_id' => $userData['user_id'], ':access_token' => $api_token,
                    ':access_token_expiry_date' => $exp->format('Y-m-d H:i:s')]);

            // Store encrypted token in Redis (not the hashed version)
            RedisUtils::redisRawInsertData($this->settings['ServerName'] . "#AuthKey#$" . $userData['user_id'],
                ['api_key' => $access_token], 60 * $this->settings['Authentication']['TokenExpiryTime']);

            $permission_acl = $userData['permission_acl'] ?
                UserUtils::GetUserPermissions(str_replace(':', ',', $userData['permission_acl'])) : [];

            // Get partner count for the user
            $partnerCount = $this->rawSelectOneRecord('dbUser',
                "SELECT COUNT(p.id) as partner_count
                 FROM partners p
                 WHERE p.user_id = :user_id AND p.status = 1",
                [':user_id' => $userData['user_id']]);

            $userPartnerCount = $partnerCount['partner_count'] ?? 0;

            // Get partner details if user has partners
            $partnerDetails = [];
            if ($userPartnerCount > 0) {
                $partnerDetails = $this->rawSelect('dbUser',
                    "SELECT p.id, p.name, p.email_address, p.address, p.country,
                            p.dial_code, p.msisdn, p.status, p.created_at
                     FROM partners p
                     WHERE p.user_id = :user_id AND p.status = 1
                     ORDER BY p.name ASC",
                    [':user_id' => $userData['user_id']]);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is successful',
                ['code' => 200, 'message' => "2FA verification successful. Partner access granted!",
                    'data' => ['token' => $api_token, 'uid' => $userData['user_id'],
                        'display_name' => $userData['display_name'],
                        'username' => $userData['user_name'],
                        'user_type' => $userData['type'],
                        'rid' => $userData['role_id'],
                        'rname' => $userData['role_name'],
                        'expires' => $this->settings['Authentication']['TokenExpiryTime'],
                        'type' => 'minute', 'permissions' => $permission_acl,
                        'partner_count' => $userPartnerCount,
                        'partners' => $partnerDetails]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * ResetUserAccountPassword
     * @return type
     */
    function ResetUserAccountPassword()
    {
        $start = $this->getMicrotime();

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . " | Request:" . json_encode($data));

        $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
        $Authorization = $headers['x-authorization'] ?? false;
        $hashKey = $headers['x-hash-key'] ?? false;
        $appKey = $headers['x-app-key'] ?? false;

        $params['username'] = $data->username ?? false;
        $params['dial_code'] = $data->dial_code ?? false;

        if (!$Authorization || !$hashKey || !$params['username'] || !$appKey) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Mandatory field(s) required!"], true);
        }

        if (!$params['dial_code']) {
            $params['dial_code'] = $this->settings['mnoApps']['DefaultDialCode'];
        }

        if (!$this->validateEmail($params['username'])) {
            if ($params['dial_code']) {
                $params['dial_code'] = $this->settings['mnoApps']['DefaultDialCode'];
            }

            $params['username'] = $this->formatMobileNumber($params['username'], $params['dial_code']);
            $params['network'] = $this->getMobileNetwork($params['username'], $params['dial_code']);
            if ($params['network'] == 'UNKNOWN') {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 422,
                        'message' => "Validation Error. Invalid Phone Number!"], true);
            }
        }

        try {
            $channel = UserUtils::GetApplicationKeys($appKey);
            if (!$channel || !UserUtils::OutboundRequest($Authorization)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Authorization Error. Invalid Key!'], true);
            }

            if (!UserUtils::HashCalculate($data, $hashKey, $appKey)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Security Error. Wrong authentictated request!'], true);
            }

            $userData = $this->rawSelectOneRecord('dbUser',
                'SELECT user.id user_id,user.user_name,user.msisdn,user_login.status,user_login.role_id'
                . ',user_login.verification_code,user_login.blocked_timeline'
                . ',user_login.reset_expiry_date,user_login.reset_attempts '
                . 'FROM user LEFT JOIN user_login ON user.id=user_login.user_id '
                . 'WHERE user.status in (1,2) AND user.user_name=:msisdn or user.msisdn=:msisdn',
                [':msisdn' => $params['username']]);

            $this->infologger->info(__LINE__ . ":" . __CLASS__
                . " | ResetUser userData:" . json_encode($userData)
                . " | ResetUser params:" . json_encode($params['username'])
            );

            if (!$userData) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Account Error. Account is Invalid!'], true);
            }

            $key = $this->settings ['ServerName'] . "#AuthKey#$" . $userData['user_id'];
//            RedisUtils::redisRawDelete($key);

            if (UserUtils::CheckBlackListAccount($userData['user_id'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => 'Authorization Denied. Account is blacklisted!'], true);
            }

            if ($userData['status'] == 3) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Account Reset Error. Account is suspended or blocked!'], true);
            }

            $this->rawUpdateWithParams('dbUser',
                "UPDATE user_login SET status=2 WHERE user_id=:user_id LIMIT 1",
                [':user_id' => $userData['user_id']]);

            if ($userData['blocked_timeline']) {
                $checkHoldTime = $this->rawSelectOneRecord('dbUser',
                    "SELECT blocked_timeline FROM user_login WHERE user_id=:user_id "
                    . "AND blocked_timeline>=NOW()", [':user_id' => $userData['user_id']]);
                if ($checkHoldTime) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 401,
                            'message' => 'Account Reset Error. Kindly wait '
                                . 'for the Lock TimeOut period to be complete to '
                                . 'initiate another reset!'], true);
                }

                $userData['reset_expiry_date'] = null;
                $userData['reset_attempts'] = 0;
            }

            if ($userData['reset_expiry_date']) {
                $resetHoldTime = $this->rawSelectOneRecord('dbUser',
                    "SELECT IFNULL(TIMEDIFF(CURRENT_TIMESTAMP(),reset_expiry_date),0) reset_minutes "
                    . "FROM user_login WHERE user_id=:user_id",
                    [':user_id' => $userData['user_id']]);
                if ($resetHoldTime ['reset_minutes'] < 0) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 423,
                            'message' => 'Account Reset Error.'
                                . 'Kindly wait for the 3 Minutes reset '
                                . 'period to be complete to initiate another reset!'], true);
                }
            }

            if ($userData['reset_attempts'] >= $this->settings['Authentication']['MaxResetAttempts']) {
                $this->rawUpdateWithParams('dbUser',
                    "UPDATE user_login SET reset_expiry_date=null"
                    . ",blocked_timeline=:blocked_time,reset_attempts=reset_attempts+1"
                    . ",failed_reset_attempts=failed_reset_attempts+1 "
                    . "WHERE user_id=:user_id LIMIT 1", [':user_id' => $userData['user_id'],
                        ':blocked_time' => date("Y-m-d H:i:s", strtotime($this->now() . " +15 minute"))]);

                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Account Reset Error. Maximum reset attempts reached!'], true);
            }

            $issuedAt = new \DateTimeImmutable();
            $exp = $issuedAt->modify('+' . $this->settings['Authentication'] ['TokenExpiryTime'] . ' minutes');

            $access_token = $this->Encrypt(json_encode(
                ['uid' => $userData['user_id'],
                    'username' => $userData['user_name'],
                    'st' => 1,
                    'rid' => $userData['role_id'],
                    'exp' => $exp->getTimestamp(),
                    'iat' => $issuedAt->getTimestamp(),
                    'nbf' => $issuedAt->getTimestamp(),
                    'iss' => md5($this->settings['ServerName']),
                    'ip' => $this->getClientIPServer()]));
            $api_token = base64_encode(md5($access_token));

            $reset_expiry_date = $issuedAt
                ->modify('+' . $this->settings['Authentication'] ['ResetDelay'] . ' minutes')
                ->format('Y-m-d H:i:s');

            $v_code = $this->randStrGen(5);

            $updateParams = [
                ':verification_code' => $v_code . "$" . $userData['user_id'],
//                ':verification_code' => $this->Encrypt(md5($v_code . "$" . $userData['user_id'])),
                ":user_id" => $userData['user_id'],
                ':access_token' => $api_token,
                ':reset_expiry_date' => $reset_expiry_date];

            $updateSql = "UPDATE user_login SET reset_attempts=0"
                . ",failed_attempts=0"
                . ",reset_expiry_date=:reset_expiry_date"
                . ",verification_code=:verification_code"
                . ",blocked_timeline=null"
                . ",last_reset_date=NOW()"
                . ",access_token_expiry_date=null"
                . ",status=2"
                . ",access_token=:access_token"
                . ",verification_code=:verification_code "
                . "WHERE user_id=:user_id LIMIT 1";

            if (!$this->rawUpdateWithParams('dbUser', $updateSql, $updateParams)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Account Reset Error. System is busy!'], true);
            }

            $msgUtils = new Messaging();

            $result = [
                'code' => 400];

            $unique_id = $this->ReferenceNumber();
            $verifyUrl = $this->settings ['Site'] . "/verify";

            if (!$this->validateEmail($params['username'])) {
                // log sending by sms
                $this->infologger->info(__LINE__ . ":" . __CLASS__
                    . "| Sending SMS to: " . $userData['display_name']
                    . "| Code: $v_code | UniqueId: $unique_id");

                $start = $this->getMicrotime();
                $result = $msgUtils->SendSMs(
                    ['msisdn' => $userData['msisdn'],
                        'message' => "Reset OTP $v_code  to verify your account."
                            . "\nLink:$verifyUrl",
                        'callback_url' => '',
                        'user_id' => $userData['user_id'],
                        'network' => 'SAFARICOM',
                        'short_code' => 'MOSSBETS_TS',
                        'message_type' => 'NOTIFICATIONS',
                        'unique_id' => $unique_id]);

                $this->infologger->info(__LINE__ . ":" . __CLASS__
                    . "|" . $this->CalculateTAT($start) . " Sec(s)"
                    . "| [" . $userData['user_name'] . "]"
                    . "| UniqueId: $unique_id"
                    . "| SendSMs()" . json_encode($result));

                if (!$result || !$result['success']) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 400,
                            'message' => "Reset Account Password Failed."], true);
                }
            } else {
                // log sending by email
                $this->infologger->info(__LINE__ . ":" . __CLASS__
                    . "| Sending Email to: " . $userData['user_name']
                    . "| Code: $v_code | UniqueId: $unique_id");

                $email_body = '<p>'
                    . '<h2>Hello, your reset OTP code is ' . '</br>' . $v_code . '</h2>'
                    . '</br>Link: ' . $verifyUrl
                    . '</br>Helpline: ' . $this->settings['Helpline']
                    . '</p>';
                $result = $msgUtils->SendEmail(
                    ['from' => '<EMAIL>',
                        'decription' => 'Password Reset Service'],
                    [$userData['user_name']], 'MB Reset Account Password',
                    $email_body);
                $this->infologger->info(__LINE__ . ":" . __CLASS__
                    . "|" . $this->CalculateTAT($start) . " Sec(s)"
                    . "| [" . $userData['user_name'] . "]"
                    . "| Code: $v_code | UniqueId: $unique_id"
                    . "| SendEmail()" . json_encode($result));


                if (!$result || $result['code'] !== 200) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 400,
                            'message' => "Reset Account Password Failed."], true);
                }
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Reset Account Password was successful. "
                        . "Verify Account to continue. OTP Expires in "
                        . "~" . $this->settings['Authentication'] ['ResetDelay'] . " Minutes",
                    'expires_on' => $reset_expiry_date]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * ChangeUserAccountPassword
     * @return type
     */
    function ChangeUserAccountPassword()
    {
        $start = $this->getMicrotime();

        $data = (array)$this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . " | Request ChangeUserAccountPassword:" . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['username', 'otp_code', 'dial_code', 'new_password'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);

            $params['username'] = $extractedData['username'] ?: false;
            $params['otp_code'] = $extractedData['otp_code'] ?: false;
            $params['dial_code'] = $extractedData['dial_code'] ?: false;
            $params['new_password'] = $extractedData['new_password'] ?: false;

            // Validate required fields
            if (!$params['username'] || !$params['new_password'] || !$params['otp_code']) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 422,
                        'message' => "Username, new password, and OTP code are required!"], true);
            }

        if (!$params['dial_code']) {
            $params['dial_code'] = $this->settings['mnoApps']['DefaultDialCode'];
        }

//        $params['otp_code'] = base64_decode($params['otp_code']);
//        $params['otp_code'] = strtoupper($params['otp_code']);
        $params['new_password'] = base64_decode($params['new_password']);

        if (!$this->validateEmail($params['username'])) {
            $params['username'] = $this->formatMobileNumber($params['username'], $params['dial_code']);
            $params['network'] = $this->getMobileNetwork($params['username'], $params['dial_code']);
            if ($params['network'] == 'UNKNOWN') {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 422,
                        'message' => 'Validation Error. Invalid Phone Number!'], true);
            }
        }

        $validation = $this->ValidatePassword($params['new_password']);
        if (!$validation) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 421,
                    'message' => 'Password should be at least 6 characters '
                        . 'in length and should include at least one upper case letter, '
                        . 'one number, or one special character!'], true);
        }


            $userData = $this->rawSelectOneRecord('dbUser',
                "SELECT user.id user_id,user.user_name,user_login.role_id,user_login.status"
                . ",user_login.verification_code,user_login.blocked_timeline,user_login.reset_attempts"
                . ",IFNULL(user_login.permission_acl,user_roles.permissions_acl) as permission_acl"
                . " FROM user "
                . "JOIN user_login ON user.id=user_login.user_id "
                . "JOIN user_roles ON user_login.role_id=user_roles.id "
                . "WHERE user.status=1  AND user_roles.status=1 AND user.user_name=:username or msisdn=:username",
                [':username' => $params['username']]);

            if (!$userData) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Account Error. Account is Invalid!'], true);
            }

            if (UserUtils::CheckBlackListAccount($userData['user_id'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 403,
                        'message' => 'Authorization Denied. Account is blacklisted!'], true);
            }

            $key = $this->settings ['ServerName'] . "#AuthKey#$" . $userData['user_id'];
//            RedisUtils::redisRawDelete($key);

            if (!in_array($userData['status'], [2, 9])) {
                if ($userData['status'] == 1) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 400,
                            'message' => 'Change Password Error! Account is active. '
                                . 'Login to continue!'], true);
                }

                if ($userData['status'] == 3) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 401,
                            'message' => 'Account Error. Account is Suspended!'], true);
                }

                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Change Password Error! Invalid Account state'], true);
            }

            if ($userData['blocked_timeline']) {
                $checkHoldTime = $this->rawSelectOneRecord('dbUser',
                    "SELECT blocked_timeline FROM user_login WHERE user_id=:user_id "
                    . "AND blocked_timeline>=NOW()", [':user_id' => $userData['user_id']]);
                if ($checkHoldTime) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 401,
                            'message' => 'Change Password Error. Kindly wait '
                                . 'for the Lock TimeOut period to be complete to '
                                . 'initiate another reset!'], true);
                }

                $userData['reset_attempts'] = 0;
                $userData['blocked_timeline'] = null;
            }

//            if ($this->Decrypt($userData['verification_code']) != $this->Encrypt(md5($params['otp_code']
//                    . "$" . $userData['user_id']))) {
            if ($userData['verification_code'] != $params['otp_code'] . "$" . $userData['user_id']) {
                if ($userData['reset_attempts'] >= $this->settings['Authentication']['MaxResetAttempts']) {
                    $userData['blocked_timeline'] = isset($userData['blocked_timeline']) ?
                        $userData['blocked_timeline'] :
                        $this->now();

                    $this->rawUpdateWithParams('dbUser',
                        "UPDATE user_login SET reset_attempts=reset_attempts+1"
                        . ",failed_reset_attempts=failed_reset_attempts+1"
                        . ",blocked_timeline=:blocked_time WHERE user_id=:user_id LIMIT 1",
                        [':user_id' => $userData['user_id'],
                            ':blocked_time' => date("Y-m-d H:i:s",
                                strtotime($userData ['blocked_timeline'] . " +15 minute"))]);

                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 401,
                            'message' => 'Change Password Error. '
                                . 'Maximum reset attempts reached!'], true);
                }

                $this->rawUpdateWithParams('dbUser',
                    "UPDATE user_login SET reset_attempts=reset_attempts+1"
                    . ",failed_reset_attempts=failed_reset_attempts+1"
                    . " WHERE user_id=:user_id LIMIT 1", [':user_id' => $userData['user_id']]);

                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Change Password Error. Wrong OTP provided!'], true);
            }

            $issuedAt = new \DateTimeImmutable();
            $exp = $issuedAt->modify('+' . $this->settings['Authentication'] ['TokenExpiryTime'] . ' minutes');

            $uData = ['uid' => $userData['user_id'],
                'username' => $userData['user_name'],
                'st' => 1,
                'rid' => $userData['role_id'],
                'exp' => $exp->getTimestamp(),
                'iat' => $issuedAt->getTimestamp(),
                'nbf' => $issuedAt->getTimestamp(),
                'iss' => md5($this->settings['ServerName']),
                'ip' => $this->getClientIPServer()];
            $access_token = $this->Encrypt(json_encode($uData));

            $api_token = base64_encode(md5($access_token));
            $access_token_expiry_date = $exp->format('Y-m-d H:i:s');
            $next_passwd_change_date = $issuedAt->modify('+' . $this
                    ->settings['Authentication'] ['NextResetDays'] . ' days')->format('Y-m-d H:i:s');

            $updateSql = "UPDATE user_login SET reset_attempts=0,activation_date=NOW()"
                . ",failed_attempts=0,reset_expiry_date=null,blocked_timeline=null"
                . ",last_reset_date=NOW(),status=:status,next_passwd_change_date=:next_passwd_change_date"
                . ",access_token_expiry_date=:access_token_expiry_date"
                . ",access_token=:access_token,status=:status "
                . "WHERE user_id=:user_id LIMIT 1";

            if (!$this->rawUpdateWithParams('dbUser',
                $updateSql,
                [':access_token' => $api_token,
                    ':status' => 1,
                    ':user_id' => $userData['user_id'],
                    ':access_token_expiry_date' => $access_token_expiry_date,
                    ':next_passwd_change_date' => $next_passwd_change_date])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 429,
                        'message' => 'Change Password Error. System is busy!'], true);
            }

            if (!$this->rawUpdateWithParams('dbUser',
                "UPDATE user SET password=:password WHERE id=:user_id LIMIT 1",
                [':user_id' => $userData['user_id'],
                    ':password' => $this
                        ->Encrypt(md5($params['new_password']
                            . "$" . $userData['user_id']))])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 429,
                        'message' => 'Change Password Error. System is busy!'], true);
            }

            RedisUtils::redisRawInsertData($key,
                ['api_key' => $access_token],
                60 * $this->settings['Authentication'] ['TokenExpiryTime'] * 1);

            $permission_acl = [];
            if ($userData['permission_acl']) {
                $permission_acl = UserUtils::GetUserPermissions(str_replace(':', ',', $userData['permission_acl']));
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => "Password reset successful. Access to secure API system granted!",
                    'data' => ['token' => $api_token,
                        'rid' => $userData['role_id'],
                        'expires' => $this->settings['Authentication']['TokenExpiryTime'],
                        'type' => 'minute',
                        'permissions' => $permission_acl]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * CreateUserAccount
     * @return
     */
    function CreateUserAccount()
    {
        $start = $this->getMicrotime();

        $permissionName = "Create System User";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . " | Request CreateUserAccount:" . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'role_id', 'user_name', 'msisdn', 'display_name', 'permissions_acl'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            $role_id = $extractedData['role_id'] ?: false;
            $username = $extractedData['user_name'] ?: false;
            $msisdn = $extractedData['msisdn'] ?: false;
            $display_name = $extractedData['display_name'] ?: false;
            $permission_acl = $extractedData['permissions_acl'] ?: false;

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                $errorData = $errorResponse['error'];
                if ($errorResponse['technical_details']) {
                    $errorData['technical_message'] = $errorResponse['technical_details'];
                }
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorData, true);
            }

            // Validate required fields
            if (!$role_id || !$display_name || !$msisdn) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 422,
                        'message' => "Role ID, display name, and mobile number are required!"], true);
            }

//        $msisdn = $this->formatMobileNumber($msisdn);
//        $msisdn = $this->validateMobile($msisdn);
//        if ($this->getMobileNetwork($msisdn) == 'UNKNOWN') {
//            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
//                200,
//                'Request is not successful',
//                ['code' => 422,
//                    'message' => "Invalid Mobile Number!"], true);
//        }

        $dial_code = $this->settings['mnoApps']['DefaultDialCode'];
        $msisdn = $this->formatMobileNumber($msisdn, $dial_code);
        $params['network'] = $this->getMobileNetwork($msisdn, $dial_code);
        if ($params['network'] == 'UNKNOWN') {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "Validation Error. Invalid Phone Number!"], true);
        }

        if ($username) {
            $username = trim($username);
            if (!$this->validateEmail($username)) {
                $dial_code = $this->settings['mnoApps']['DefaultDialCode'];
                $username = $this->formatMobileNumber($username, $dial_code);
                $network = $this->getMobileNetwork($username, $dial_code);
                if ($network == 'UNKNOWN') {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 422,
                            'message' => 'Validation Error. Invalid Phone number!'], true);
                }
            }
        }


            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                $errorData = $errorResponse['error'];
                if ($errorResponse['technical_details']) {
                    $errorData['technical_message'] = $errorResponse['technical_details'];
                }
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorData, true);
            }

            $check = $this->rawSelectOneRecord('dbUser',
                "SELECT * FROM user WHERE user_name=:user_name", [':user_name' => $username]);
            if ($check) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "A similar system user already exists!"], true);
            }

            $uRoles = $this->rawSelectOneRecord('dbUser',
                "SELECT id,name,permissions_acl FROM user_roles WHERE id=:id "
                . "AND status=1", [':id' => $role_id]);
            $role_id = $uRoles['id'] ?? false;
            if (!$uRoles || !$role_id) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "User role is not available!"], true);
            }

            $msgUtils = new Messaging();
            $issuedAt = new \DateTimeImmutable();
            $password = $this->randStrGen(6);

            $verifyUrl = $this->settings['Site'] . "/verify";

            if ($role_id == 1) {
                $sUser = $this->rawSelectOneRecord('dbUser',
                    "SELECT id FROM user_login WHERE role_id=:role_id", [":role_id" => 1]);
                if ($sUser) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 400,
                            'message' => "There can only be one Super Administrator!"], true);
                } else {
                    if (!$sUser) {
                        $permission_arr = $this->rawSelectOneRecord("dbUser",
                            "SELECT group_concat(id separator ':') as acl "
                            . "FROM user_permissions WHERE status=1");

                        $userId = $this->rawInsertBulk('dbUser',
                            'user',
                            ['user_name' => $this->settings['Account']['SuperAdmin']['emailAddress'],
                                'msisdn' => ************,
                                'password' => $this->Encrypt($password),
                                'display_name' => $this->settings['Account']['SuperAdmin']['displayName'],
                                'status' => 1,
                                'created_at' => $this->now(),
                                'created_by' => 0]);

                        $exp = $issuedAt->modify('+' . $this
                                ->settings['Authentication'] ['TokenExpiryTime'] . ' minutes');

                        $uLoginId = $this->rawInsertBulk('dbUser',
                            'user_login',
                            ['user_id' => $userId,
                                'role_id' => 1,
                                'permission_acl' => $permission_arr['acl'],
                                'verification_code' => $this->Encrypt(md5($password . "$" . $userId)),
                                'access_token' => base64_encode(md5($this->Encrypt(json_encode(
                                    ['uid' => $userId,
                                        'username' => $username,
                                        'st' => 1,
                                        'rid' => 1,
                                        'exp' => $exp->getTimestamp(),
                                        'iat' => $issuedAt->getTimestamp(),
                                        'nbf' => $issuedAt->getTimestamp(),
                                        'iss' => md5($this->settings['ServerName']),
                                        'ip' => $this->getClientIPServer()])))),
                                'access_token_expiry_date' => $exp->format('Y-m-d H:i:s'),
                                'next_passwd_change_date' => $issuedAt->modify('+'
                                    . $this->settings['Authentication'] ['NextResetDays']
                                    . ' days')->format('Y-m-d H:i:s'),
                                'status' => $this->settings['UserStatus']['Unverified'],
                                'created_at' => $this->now()]);

                        if (!$this->rawUpdateWithParams('dbUser',
                            "UPDATE user SET password=:password WHERE id=:user_id LIMIT 1",
                            [':user_id' => $userId,
                                ':password' => $this->Encrypt(md5($password
                                    . "$" . $userId))])) {
                            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                                200,
                                'Request is not successful',
                                ['code' => 429,
                                    'message' => 'Change Password Error. System is busy!'], true);
                        }

                        if ($this->validateEmail($this->settings['Account']['SuperAdmin']['emailAddress'])) {
                            $result = $msgUtils->SendEmail(
                                ['from' => '<EMAIL>', 'decription' => 'OTP Notification Service'],
                                [$this->settings['Account']['SuperAdmin']['emailAddress']],
                                'Verify New Account OTP',
                                '<p><h2>Hello, your Verify Temp Password is ' . '</br>' . $password . '</h2>'
                                . '</br>Link: ' . $verifyUrl
                                . '</br>Helpline: ' . $this->settings ['Helpline'] . '</p>');
                            $this->infologger->info(__LINE__ . ":" . __CLASS__
                                . "|" . $this->CalculateTAT($start) . " Sec(s)"
                                . "| [" + $this->settings['Account']['SuperAdmin']['emailAddress'] + "]"
                                . "| SendEmail()" . json_encode($result));
                        } else {
                            $unique_id = $this->ReferenceNumber();

                            $result = $msgUtils->SendSMs(
                                ['msisdn' => $this->settings['Account']['SuperAdmin']['emailAddress'],
                                    'user_id' => $userId,
                                    'message' => "Mossbets BO Account created use."
                                        . "\n-\nTemp Password: $password"
                                        . "\n:Verify Link " . $verifyUrl,
                                    'callback_url' => '',
                                    'message_type' => 'NOTIFICATIONS',
                                    'unique_id' => $unique_id]);
                            $this->infologger->info(__LINE__ . ":" . __CLASS__
                                . "|" . $this->CalculateTAT($start) . " Sec(s)"
                                . "| [" + $this->settings['Account']['SuperAdmin']['emailAddress'] + "]"
                                . "| UniqueId: $unique_id"
                                . "| SendSMs()" . json_encode($result));
                        }
                    }
                }
            }

            $password = $this->randStrGen(6);

            if ($permission_acl) {
                $permission_arr = explode(':', $permission_acl);
                $permission_arr = array_unique($permission_arr);
                $permission_check_arr = implode(',', $permission_arr);

                $this->infologger->info(__LINE__ . ":" . __CLASS__
                    . "|" . $this->CalculateTAT($start) . " Sec(s)"
                    . "| [$username]"
                    . "| Permissions()==>" . $permission_check_arr);

                $permissions = $this->rawSelectOneRecord("dbUser",
                    "SELECT group_concat(id separator ':') as acl "
                    . "FROM user_permissions WHERE status=1 AND id IN($permission_check_arr)");
                $permission_arr = $permissions['acl'] ?? false;
                if (!$permission_arr) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 400,
                            'message' => "Invalid user permissions!"], true);
                }

                $permission_arr = explode(':', $permission_acl);
                $permission_arr = array_unique($permission_arr);
                if (UserUtils::CheckSuperUserPermission($permission_arr)) {
                    if ($role_id != 1) {
                        if (($key = array_search("1", $permission_arr)) !== false) {
                            unset($permission_arr[$key]);
                        }
                    }
                    $permission_arr = array_values($permission_arr);
                }

                $permission_acl = implode(':', $permission_arr);

                $this->infologger->info(__LINE__ . ":" . __CLASS__
                    . "|" . $this->CalculateTAT($start) . " Sec(s)"
                    . "| [$username]"
                    . "| Permissions() =>" . $permission_acl);
            }

            $userId = $this->rawInsertBulk('dbUser',
                'user',
                ['user_name' => $username,
                    'msisdn' => $msisdn,
                    'password' => $password,
                    'display_name' => $display_name,
                    'status' => 1,
                    'created_at' => $this->now(),
                    'created_by' => $authResult['user_id']]);

            $exp = $issuedAt->modify('+' . $this->settings['Authentication'] ['TokenExpiryTime'] . ' minutes');

            $uLoginId = $this->rawInsertBulk('dbUser',
                'user_login',
                ['user_id' => $userId,
                    'role_id' => $role_id,
                    'permission_acl' => $permission_acl,
                    'verification_code' => $this->Encrypt(md5($password . "$" . $userId)),
                    'access_token' => base64_encode(md5($this->Encrypt(json_encode(
                        ['uid' => $userId,
                            'username' => $username,
                            'st' => 1,
                            'rid' => $role_id,
                            'exp' => $exp->getTimestamp(),
                            'iat' => $issuedAt->getTimestamp(),
                            'nbf' => $issuedAt->getTimestamp(),
                            'iss' => md5($this->settings['ServerName']),
                            'ip' => $this->getClientIPServer()])))),
                    'access_token_expiry_date' => $exp->format('Y-m-d H:i:s'),
                    'next_passwd_change_date' => $issuedAt->modify('+'
                        . $this->settings['Authentication'] ['NextResetDays']
                        . ' days')->format('Y-m-d H:i:s'),
                    'status' => $this->settings['UserStatus']['Unverified'],
                    'created_at' => $this->now()]);

            if (!$this->rawUpdateWithParams('dbUser',
                "UPDATE user SET password=:password,status=1 "
                . "WHERE id=:user_id LIMIT 1",
                [':user_id' => $userId,
                    ':password' => $this->Encrypt(md5($password
                        . "$" . $userId))])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 429,
                        'message' => 'Change Password Error. System is busy!'], true);
            }

            $msgUtils = new Messaging();
            $unique_id = $this->ReferenceNumber();

            if ($this->validateEmail($username)) {
                $result = $msgUtils->SendEmail(
                    ['from' => '<EMAIL>', 'decription' => 'OTP Notification Service'],
                    [$username],
                    'Verify New Account OTP',
                    '<p><h2>Hello, your Verify Temp Password is ' . '</br>' . $password . '</h2>'
                    . '</br>Link: ' . $verifyUrl
                    . '</br>Helpline: ' . $this->settings ['Helpline'] . '</p>');
                $this->infologger->info(__LINE__ . ":" . __CLASS__
                    . "|" . $this->CalculateTAT($start) . " Sec(s)"
                    . "| [$username]"
                    . "| SendEmail()" . json_encode($result));
            } else {
                $unique_id = $this->ReferenceNumber();

                $result = $msgUtils->SendSMs(
                    ['msisdn' => $username,
                        'user_id' => $userId,
                        'message' => "Mossbets BO Account created use."
                            . "\n-\nTemp Password: $password"
                            . "\n:Verify Link " . $verifyUrl,
                        'callback_url' => '',
                        'message_type' => 'NOTIFICATIONS',
                        'unique_id' => $unique_id]);
                $this->infologger->info(__LINE__ . ":" . __CLASS__
                    . "|" . $this->CalculateTAT($start) . " Sec(s)"
                    . "| [$username]"
                    . "| UniqueId: $unique_id"
                    . "| SendSMs()" . json_encode($result));
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => $uRoles['name'] . " User Account Created Successfully!"]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * GetPartnerUsers
     * @return type
     */
    function GetSystemUsers()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Users";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetSystemUsers:" . json_encode($data));

        try {
            // Authentication
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());
            $requestFields = ['timestamp', 'user_name', 'phone_number', 'type', 'start', 'end', 'status', 'export', 'limit', 'page', 'skip_cache', 'sort'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            $params = [
                'user_name'   => $extractedData['user_name'] ?: false,
                'phone_number'=> $extractedData['phone_number'] ?: false,
                'type'        => $extractedData['type'] ?: false,
                'limit'       => $extractedData['limit'] ?: $this->settings['SelectRecordLimit'],
                'skipCache'   => in_array($extractedData['skip_cache'], [1, 2]) ? $extractedData['skip_cache'] : 1,
                'sort'        => $extractedData['sort'] ?: false
            ];

            $start  = $extractedData['start'] ?: false;
            $stop   = $extractedData['end'] ?: false;
            $status = $extractedData['status'] ?: false;
            $export = $extractedData['export'] ?: false;
            $page   = $extractedData['page'] ?: 1;

            // Authorization
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                $errorData = $errorResponse['error'];
                if ($errorResponse['technical_details']) {
                    $errorData['technical_message'] = $errorResponse['technical_details'];
                }
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorData, true);
            }

            // Sorting
            $order_arr = explode("|", $this->cleanStrSQL($params['sort']));
            if (count($order_arr) > 1) {
                $sort = "u." . $this->cleanStrSQL($order_arr[0]);
                $order = strtoupper($order_arr[1]) === 'ASC' ? 'ASC' : 'DESC';
            } else {
                $sort = 'u.id';
                $order = 'DESC';
            }

            // Build search query
            $searchParts = [];
            $searchParams = [];

            if ($params['type']) {
                $searchParts[] = "u.type = :type";
                $searchParams[':type'] = $params['type'];
            }
            if ($params['phone_number']) {
                $searchParts[] = "u.msisdn = :phone_number";
                $searchParams[':phone_number'] = $params['phone_number'];
            }
            if ($status) {
                $searchParts[] = "ul.status = :status";
                $searchParams[':status'] = $status;
            }
            if ($params['user_name']) {
                $searchParts[] = "u.user_name REGEXP :user_name";
                $searchParams[':user_name'] = $params['user_name'];
            }
            if ($start && $stop) {
                $searchParts[] = "u.created_at BETWEEN :start AND :stop";
                $searchParams[':start'] = "$start 00:00:00";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($stop && !$start) {
                $searchParts[] = "u.created_at <= :stop";
                $searchParams[':stop'] = "$stop 23:59:59";
            } elseif ($start && !$stop) {
                $searchParts[] = "u.created_at >= :start";
                $searchParams[':start'] = "$start 00:00:00";
            }

            $searchQuery = count($searchParts) ? "WHERE " . implode(" AND ", $searchParts) : "";

            $sorting = $this->tableQueryBuilder($sort, $order, $page, $params['limit']);
            if ($export == 1) {
                $sorting = "";
            }

            // Main SQL
            $sql = "
            SELECT 
                COUNT(*) OVER() AS trx_count,
                u.id AS user_id, 
                u.msisdn, 
                u.display_name, 
                u.user_name,
                u.status, 
                u.type, 
                ul.status AS login_status,
                ul.role_id, 
                ur.name AS role_name, 
                ul.success_attempts,
                ul.failed_reset_attempts, 
                ul.failed_attempts,
                ul.cumlative_failed_attempts, 
                ul.cumulative_success_login,
                ul.blocked_timeline, 
                ul.reset_expiry_date,
                ul.last_logged_on, 
                ul.activation_date, 
                ul.last_reset_date,
                ul.next_passwd_change_date, 
                ul.created_at,
                p.id AS partner_id, 
                p.name AS partner_name, 
                p.email_address AS partner_email, 
                p.address AS partner_address,
                p.country AS partner_country, 
                p.dial_code AS partner_dial_code, 
                p.msisdn AS partner_msisdn, 
                p.status AS partner_status, 
                p.created_at AS partner_created_at
            FROM user u
            LEFT JOIN user_login ul ON u.id = ul.user_id
            LEFT JOIN user_roles ur ON ul.role_id = ur.id
            LEFT JOIN partners p ON u.id = p.user_id
            $searchQuery
            $sorting
        ";

            $results = $this->rawSelect('dbUser', $sql, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Request returned no partner users!'], true);
            }

            // Format output
            $arr = [];
            foreach ($results as $result) {
                $result['partner_info'] = [
                    'id' => $result['partner_id'],
                    'name' => $result['partner_name'],
                    'email' => $result['partner_email'],
                    'address' => $result['partner_address'],
                    'country' => $result['partner_country'],
                    'dial_code' => $result['partner_dial_code'],
                    'msisdn' => $result['partner_msisdn'],
                    'status' => $result['partner_status'],
                    'created_at' => $result['partner_created_at']
                ];
                unset($result['partner_id'], $result['partner_name'], $result['partner_email'], $result['partner_address'], $result['partner_country'], $result['partner_dial_code'], $result['partner_msisdn'], $result['partner_status'], $result['partner_created_at']);
                $arr[] = $result;
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Queried ' . $arr[0]['trx_count'] . ' partner users successfully!',
                    'data' => ['record_count' => $arr[0]['trx_count'], 'data' => $arr]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "|TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }


    /**
     * EditUserAccount
     * @param type $user_id
     * @return type
     */
    function EditUserAccount($user_id)
    {
        $start = $this->getMicrotime();

        $permissionName = "Edit System Users";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . " | Request EditUserAccount:" . json_encode($data)
        );

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'status', 'role_id', 'display_name', 'user_name', 'permissions_acl'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            $status = $extractedData['status'] ?: false;
            $role_id = $extractedData['role_id'] ?: false;
            $display_name = $extractedData['display_name'] ?: false;
            $username = $extractedData['user_name'] ?: false;
            $permissions_acl = $extractedData['permissions_acl'] ?: false;

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                $errorData = $errorResponse['error'];
                if ($errorResponse['technical_details']) {
                    $errorData['technical_message'] = $errorResponse['technical_details'];
                }
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorData, true);
            }

            // Validate required fields
            if (!$user_id || !$role_id) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    422,
                    'Request is not successful',
                    ['code' => 422,
                        'message' => "User ID and role ID are required!"], true);
            }

            $status = (int)$status;
            if (!in_array($status,
                [$this->settings['UserStatus']['Unverified'],
                    $this->settings['UserStatus']['InActive'],
                    $this->settings['UserStatus']['Active'],
                    $this->settings['UserStatus']['Dormant'],
                    $this->settings['UserStatus']['Suspended']])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    422,
                    'Request is not successful',
                    ['code' => 422,
                        'message' => "Invalid user status"], true);
            }

            $results = $this->rawSelectOneRecord('dbUser',
                "SELECT user.id,user.user_name,user.status,user_login.status"
                . ",user_roles.name role_name,user_login.role_id,user_roles.status role_state"
                . ",user_login.activation_date,user.created_at FROM user "
                . "JOIN user_login ON user.id=user_login.user_id "
                . "JOIN user_roles ON user_login.role_id=user_roles.id "
                . "WHERE user.id=:id", [':id' => $user_id]);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "User account doesn't exists!"], true);
            }

            if ($results['role_id'] == 1) {
                if ($authData['role_id'] != 1) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 400,
                            'message' => $results['role_name'] . " User account "
                                . "cannot be altered by a user with lower access!"], true);
                }
            }

            $roles = $this->rawSelectOneRecord("dbUser",
                'SELECT * FROM user_roles WHERE id=:id and status=1',
                [':id' => $role_id]);

            if (!$roles) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Sorry, User Role doesn't exists!"], true);
            }

            if ($roles['id'] == 1) {
                $adminChecks = $this->rawSelectOneRecord('dbUser',
                    "SELECT count(id) FROM user_login where role_id=1", []);

                if ($adminChecks) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 400,
                            'message' => "Sorry, Cannot have more "
                                . "than one Super Adminstrators!"], true);
                }
            }

            $sql_log_params = [
                ':user_id' => $user_id,
                ':role_id' => $role_id,
            ];

            $sql_params = [':user_id' => $user_id];

            // Only update user_login.status if it's specifically intended to force password reset
            // For regular status updates (activate/deactivate), preserve current login status
            $force_password_reset = $data['force_password_reset'] ?? false;

            if ($force_password_reset) {
                // Force password reset by setting login status to 2 (requires password reset)
                $sql_log_params[':status'] = 2;
                $sql_log = "UPDATE user_login SET status=:status,role_id=:role_id,updated_at=now()";
            } else {
                // Preserve current login status for regular user status updates
                $sql_log = "UPDATE user_login SET role_id=:role_id,updated_at=now()";
            }
            if ($permissions_acl) {
                if (is_array($permissions_acl)) {
                    $permission_arr = $permissions_acl; // Already an array
                } else {
                    $permission_arr = explode(':', $permissions_acl);
                }

                if (count($permission_arr) > 0) {
                    $permission_arr = array_unique($permission_arr);
                }

                $permission_check_arr = implode(',', $permission_arr);

                $permissions = $this->rawSelectOneRecord("dbUser",
                    "SELECT group_concat(id separator ':') as acl "
                    . "FROM user_permissions WHERE status=1 AND id IN($permission_check_arr)");

                $permission_arr = $permissions['acl'] ?? false;

                if (!$permission_arr) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 400,
                            'message' => "Invalid user permissions!"], true);
                }

                if (UserUtils::CheckSuperUserPermission((array)$permission_check_arr)) {
                    if ($role_id != 1) {
                        if (($key = array_search("1", $permission_arr)) !== false) {
                            unset($permission_arr[$key]);
                        }
                        $permission_arr = array_values($permission_arr);
                    }
                }

                if (!is_array($permission_arr)) {
                    $permission_arr = explode(":", $permission_arr); // Convert string to array
                }
                // Ensure it's an array before using implode()
                $sql_log_params['permission_acl'] = implode(',', $permission_arr);

                $sql_log .= ", permission_acl = :permission_acl";
            }

            $sql_log .= " WHERE user_id = :user_id LIMIT 1";

            // Update user table with the provided status (not hardcoded to 1)
            $sql_user = "UPDATE user SET status=:user_status,updated_at=NOW()";
            $sql_params[':user_status'] = $status;
            if ($display_name) {
                $sql_user .= ",display_name=:display_name";
                $sql_params[':display_name'] = $display_name;
            }

            if ($username) {
                $sql_user .= ",user_name=:user_name";
                $sql_params[':user_name'] = $username;
            }

            $sql_user .= " WHERE id=:user_id LIMIT 1";

            if (!$this->rawUpdateWithParams('dbUser', $sql_log, $sql_log_params)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Sorry, User Edit failed. Reason: system busy!."], true);
            }

            if (!$this->rawUpdateWithParams('dbUser', $sql_user, $sql_params)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Sorry, User Edit failed. Reason: system busy!"], true);
            }

            // Invalidate Redis cache for user data
            $userCacheKeys = [
                "user:profile:" . $user_id,
                "user:permissions:" . $user_id,
                "user:session:" . $user_id,
                $this->settings['ServerName'] . "#AuthKey#$" . $user_id
            ];

//            foreach ($userCacheKeys as $cacheKey) {
//                RedisUtils::redisRawDelete($cacheKey);
//            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => $results['user_name'] . " User Account updated Successfully!",
                    'data' => [
                        'user_id' => $user_id,
                        'username' => $results['user_name'],
                        'status' => $status,
                        'role_id' => $role_id,
                        'force_password_reset' => $force_password_reset
                    ]
                ]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * ResendUserOTP
     * @return type
     */
    function ResendUserOTP($user_id)
    {
        $start = $this->getMicrotime();
        $permissionName = "Resend System User OTP";

        // Extract request data using ControllerHelper pattern
        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . " | Request ResendUserOTP:" . $user_id . "::::" . json_encode($data));

        // Extract authentication headers using ControllerHelper
        $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());
        $authData['timestamp'] = $data['timestamp'] ?? false;

        // Validate required parameters
        if (!$user_id) {
            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 422,
                    'message' => "User ID is required!"], true);
        }

        try {
            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                $errorData = $errorResponse['error'];
                if ($errorResponse['technical_details']) {
                    $errorData['technical_message'] = $errorResponse['technical_details'];
                }
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorData, true);
            }

            $results = $this->rawSelectOneRecord('dbUser',
                "SELECT user.id,user.user_name,user.status,user_login.status"
                . ",user_roles.name role_name,user_roles.id role_id"
                . ",user_roles.status role_state,user_login.activation_date"
                . ",user.created_at FROM user "
                . "JOIN user_login ON user.id=user_login.user_id "
                . "JOIN user_roles ON user_login.role_id=user_roles.id "
                . "WHERE user.id=:id and user_roles.status=1 and user.status=1", [':id' => $user_id]);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "User account doesn't exists!"], true);
            }

            // Additional role-based validation
            if ($results['role_id'] == 1) {
                if ($authResult['user_data']['role_id'] != 1) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200,
                        'Request is not successful',
                        ['code' => 400,
                            'message' => $results['role_name'] . " User account "
                                . "cannot be altered by a user with lower access!"], true);
                }
            }

            if ($results['status'] == $this->settings['UserStatus']['Suspended']) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Sorry, User account is "
                            . "suspended on all system actions. "
                            . "Contact System Administrator!"], true);
            }

            $issuedAt = new \DateTimeImmutable();
            $exp = $issuedAt->modify('+' . $this->settings['Authentication'] ['TokenExpiryTime'] . ' minutes');

            $access_token = $this->Encrypt(json_encode(
                ['uid' => $results['id'],
                    'username' => $results['user_name'],
                    'st' => 1,
                    'rid' => $results['role_id'],
                    'exp' => $exp->getTimestamp(),
                    'iat' => $issuedAt->getTimestamp(),
                    'nbf' => $issuedAt->getTimestamp(),
                    'iss' => md5($this->settings['ServerName']),
                    'ip' => $this->getClientIPServer()]));
            $api_token = base64_encode(md5($access_token));

            $reset_expiry_date = $issuedAt
                ->modify('+' . $this->settings['Authentication'] ['ResetDelay'] . ' minutes')
                ->format('Y-m-d H:i:s');

            $v_code = $this->randStrGen(5);

            $updateParams = [
                ':verification_code' => $this->Encrypt(md5($v_code . "$" . $results['id'])),
                ":user_id" => $results['id'],
                ':access_token' => $api_token,
                ':reset_expiry_date' => $reset_expiry_date];

            $updateSql = "UPDATE user_login SET reset_attempts=0"
                . ",failed_attempts=0"
                . ",reset_expiry_date=:reset_expiry_date"
                . ",verification_code=:verification_code"
                . ",blocked_timeline=null,last_reset_date=NOW()"
                . ",access_token_expiry_date=null,status=2"
                . ",access_token=:access_token,verification_code=:verification_code "
                . "WHERE user_id=:user_id LIMIT 1";

            if (!$this->rawUpdateWithParams('dbUser', $updateSql, $updateParams)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 401,
                        'message' => 'Account Reset Error. System is busy!']);
            }

            $msgUtils = new Messaging();

            $result = ['code' => 400];

            $unique_id = $this->ReferenceNumber();
            if (!$this->validateMobile($results['msisdn'])) {
                $start = $this->getMicrotime();

                $result = $msgUtils->SendSMs(
                    ['msisdn' => $results['msisdn'],
                        'message' => "Reset OTP $v_code  to verify your account",
                        'callback_url' => '',
                        'user_id' => $results['id'],
                        'network' => 'SAFARICOM',
                        'short_code' => 'MOSSBETS_TS',
                        'message_type' => 'NOTIFICATIONS',
                        'unique_id' => $unique_id]
                );

                $this->infologger->info(__LINE__ . ":" . __CLASS__
                    . "|" . $this->CalculateTAT($start) . " Sec(s)"
                    . "| [" . $results['msisdn'] . "]"
                    . "| UniqueId: $unique_id"
                    . "| SendSMs()" . json_encode($result));
            } else {
                $email_body = '<p>'
                    . '<h2>Hello, your reset OTP code is ' . '</br>' . $v_code . '</h2>'
                    . '</br>Link: ' . $this->settings ['AdminWebURL']
                    . '</br>Helpline: ' . $this->settings ['Helpline']
                    . '</p>';
                $result = $msgUtils->SendEmail(
                    ['from' => '<EMAIL>',
                        'decription' => 'Dashboard Notification Service'],
                    [$results['user_name']], 'MB Reset Account Password',
                    $email_body);
                $this->infologger->info(__LINE__ . ":" . __CLASS__
                    . "|" . $this->CalculateTAT($start) . " Sec(s)"
                    . "| [" . $results['user_name'] . "]"
                    . "| UniqueId: $unique_id"
                    . "| SendSMs()" . json_encode($result));
            }

            if (!$result || ($result['code'] != 200)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 400,
                        'message' => "Reset Account Password Failed."], true);
            }

            // Invalidate user cache after OTP resend
            $userCacheKeys = [
                "user:profile:" . $results['id'],
                "user:session:" . $results['id'],
                $this->settings['ServerName'] . "#AuthKey#$" . $results['id']
            ];

//            foreach ($userCacheKeys as $cacheKey) {
//                RedisUtils::redisRawDelete($cacheKey);
//            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => "Reset Account Password was successful. "
                        . "Verify Account to continue. OTP Expires in "
                        . "~" . $this->settings['Authentication'] ['ResetDelay'] . " Minutes",
                    'data' => [
                        'user_id' => $results['id'],
                        'username' => $results['user_name'],
                        'expires_on' => $reset_expiry_date,
                        'expiry_minutes' => $this->settings['Authentication'] ['ResetDelay']
                    ]
                ]);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * GetUserRoles
     * @return type
     */
    function GetUserRoles()
    {
        $start = $this->getMicrotime();

        $permissionName = "View System Users Roles";

        $data = (array)$this->request->get();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . " | Request:" . json_encode($this->request->getJsonRawBody()));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                $errorData = $errorResponse['error'];
                if ($errorResponse['technical_details']) {
                    $errorData['technical_message'] = $errorResponse['technical_details'];
                }
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorData, true);
            }

            $results = $this->rawSelect('dbUser',
                "SELECT (select count(id) from user_roles)trx_count"
                . ",id role_id, name role_name, description role_desc,permissions_acl,status "
                . "FROM user_roles", []);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "User roles doesn't exists!"], true);
            }

            $arr = [];
            foreach ($results as $result) {
                $result['permissions'] = UserUtils
                    ::GetUserPermissions(str_replace(':', ',', $result['permissions_acl']));
                unset($result['permissions_acl']);
                $arr[] = $result;
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $arr[0]['trx_count']
                        . ' user roles successfully!',
                    'data' => ['total' => $arr[0]['trx_count'],
                        'data' => $arr]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * GetUserPermissions
     * @return type
     */
    function GetUserPermissions()
    {
        $start = $this->getMicrotime();

        $permissionName = "View User permissions";

//        $data = (array)$this->request->get();
        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . " | Request GetUserPermissions: " . json_encode($data)
        );

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                $errorData = $errorResponse['error'];
                if ($errorResponse['technical_details']) {
                    $errorData['technical_message'] = $errorResponse['technical_details'];
                }
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorData, true);
            }

            $results = $this->rawSelect('dbUser',
                "SELECT (select count(id) from user_permissions)trx_count"
                . ",id permission_id, name permission, description permission_desc"
                . ",status,user_id created_by FROM user_permissions", []);
            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "User permisions doesn't exists!"], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' user permisions successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }


    /**
     * GetPartnerUser - Get complete partner user profile data including partner associations
     * @return type
     */
    function GetPartnerUsers()
    {
        $start = $this->getMicrotime();
        $permissionName = "View Partner User Profile";

        // Extract request data using ControllerHelper pattern
        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . " | Request GetPartnerUser:" . json_encode($data));

        // Extract authentication headers using ControllerHelper
        $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());
        $authData['timestamp'] = $data['timestamp'] ?? false;

        try {
            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                $errorData = $errorResponse['error'];
                if ($errorResponse['technical_details']) {
                    $errorData['technical_message'] = $errorResponse['technical_details'];
                }
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorData, true);
            }

            $userId = $authResult['user_data']['user_id'];

            // Check Redis cache first
            $cacheKey = "partner:user:profile:" . $userId;
//            $cachedProfile = RedisUtils::redisRawSelectData($cacheKey);

//            if ($cachedProfile && isset($cachedProfile->user_id)) {
//                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
//                    200,
//                    'Request is successful',
//                    [
//                        'code' => 200,
//                        'message' => "Partner user profile retrieved successfully!",
//                        'data' => (array)$cachedProfile
//                    ], false);
//            }

            // Get complete partner user profile data - only Partner type users
            $userProfile = $this->rawSelectOneRecord('dbUser',
                "SELECT u.id, u.user_name, u.display_name, u.msisdn, u.email, u.type, u.status as user_status,
                        ul.role_id, ul.status as login_status, ul.last_logged_on, ul.activation_date,
                        ul.created_at, ul.updated_at, ul.permission_acl,
                        ur.name as role_name, ur.description as role_description,
                        IFNULL(ul.permission_acl, ur.permissions_acl) as effective_permissions
                 FROM user u
                 JOIN user_login ul ON u.id = ul.user_id
                 JOIN user_roles ur ON ul.role_id = ur.id
                 WHERE u.id = :user_id AND u.type = 'Partner' AND u.status = 1 AND ur.status = 1",
                [':user_id' => $userId]);

            if (!$userProfile) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => "Partner user profile not found or user is not a partner!"], true);
            }

            // Get user's partner associations (direct relationship)
            $userPartners = $this->rawSelect('dbUser',
                "SELECT p.id, p.name, p.email_address, p.address, p.country,
                        p.dial_code, p.msisdn, p.status, p.created_at, p.updated_at
                 FROM partners p
                 WHERE p.user_id = :user_id AND p.status = 1
                 ORDER BY p.name ASC",
                [':user_id' => $userId]);

            // Get user permissions
            $permissions = [];
            if ($userProfile['effective_permissions']) {
                $permissions = UserUtils::GetUserPermissions(str_replace(':', ',', $userProfile['effective_permissions']));
            }

            // Build response data with nested structure for partner user
            $profileData = [
                'user_id' => $userProfile['id'],
                'username' => $userProfile['user_name'],
                'display_name' => $userProfile['display_name'],
                'msisdn' => $userProfile['msisdn'],
                'email' => $userProfile['email'],
                'user_type' => $userProfile['type'],
                'user_status' => $userProfile['user_status'],
                'login_status' => $userProfile['login_status'],
                'last_logged_on' => $userProfile['last_logged_on'],
                'activation_date' => $userProfile['activation_date'],
                'created_at' => $userProfile['created_at'],
                'updated_at' => $userProfile['updated_at'],
                'role' => [
                    'id' => $userProfile['role_id'],
                    'name' => $userProfile['role_name'],
                    'description' => $userProfile['role_description']
                ],
                'permissions' => $permissions,
                'partners' => [
                    'total' => count($userPartners),
                    'data' => $userPartners
                ]
            ];

            // Cache the profile data for 30 minutes
//            RedisUtils::redisRawInsertData($cacheKey, $profileData, 1800);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => "Partner user profile retrieved successfully!",
                    'data' => $profileData
                ], false);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "|TAT " . $this->CalculateTAT($start) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

}

